export const Path = {
  updateService: 'service',
  providerService: 'service/providerService',
  sendlocation: 'service/getstates',
  getSearchResult: 'service/',
  getAllSubCategory: '/subCategory',
  getAllCategory: '/category',
  getAllCategoryAndSub: '/category/with-subcategories',
  checkBookingAvailability: '/api/v1/booking/availability',
  // Review CRUD Operations
  reviews: '/api/v1/reviews',
  reviewsCreate: '/api/v1/reviews',
  reviewsFrontend: '/api/v1/reviews/frontend',
  reviewById: (reviewId: string) => `/api/v1/reviews/${reviewId}`,
  reviewUpdate: (reviewId: string) => `/api/v1/reviews/${reviewId}`,
  reviewDelete: (reviewId: string) => `/api/v1/reviews/${reviewId}`,

  // Rating & Analytics
  reviewAnalytics: '/api/v1/reviews/analytics',
  reviewOverallRating: '/api/v1/reviews/rating/overall',
  reviewMultipleRatings: '/api/v1/reviews/rating/multiple',

  // Provider Responses & Replies
  reviewProviderResponse: (reviewId: string) => `/api/v1/reviews/${reviewId}/response`,
  reviewResponseReply: (reviewId: string) => `/api/v1/reviews/${reviewId}/response/reply`,
  reviewReplies: (reviewId: string) => `/api/v1/reviews/${reviewId}/replies`,
  reviewReplyUpdate: (reviewId: string, replyId: string) => `/api/v1/reviews/${reviewId}/reply/${replyId}`,
  reviewReplyDelete: (reviewId: string, replyId: string) => `/api/v1/reviews/${reviewId}/reply/${replyId}`,
  bookings: '/api/v1/booking',
  getStaff: '/staff',
  getProvider: '/user', // Added missing getProvider path
  // Two-Factor Authentication endpoints
  sendEmail2FA: '/api/v1/auth/sendEmail2FA',
  verifyEmail2FA: '/api/v1/auth/verifyEmail2FA',
  check2FAStatus: '/api/v1/auth/2fa/status',
  disable2FA: '/api/v1/auth/2fa/disable',
};
